import type * as SchemaTypes from '../types';

import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { MobilityApplicationExpiredFragment } from '../fragments/MobilityApplicationExpired';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from '../fragments/CustomerSpecs';
import type { LocalCustomerDataFragment } from '../fragments/LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from '../fragments/LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from '../fragments/CorporateCustomerData';
import type { GuarantorDataFragment } from '../fragments/GuarantorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { MobilityBookingLocationHomeDataFragment } from '../fragments/MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from '../fragments/MobilityBookingLocationPickupData';
import type { ApplicationJourneyDeposit_ApplicationAdyenDeposit_Fragment, ApplicationJourneyDeposit_ApplicationFiservDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPayGateDeposit_Fragment, ApplicationJourneyDeposit_ApplicationPorscheDeposit_Fragment, ApplicationJourneyDeposit_ApplicationTtbDeposit_Fragment } from '../fragments/ApplicationJourneyDeposit';
import type { PromoCodeDataFragment } from '../fragments/PromoCodeData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { DealerFragmentFragment } from '../fragments/DealerFragment';
import type { CompanyContextDataFragment } from '../fragments/CompanyContextData';
import type { LanguagePackContextDataFragment } from '../fragments/LanguagePackContextData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { AvailableModulesDataFragment } from '../fragments/AvailableModulesData';
import type { CompanyDealerDataFragment } from '../fragments/CompanyDealerData';
import type { MaintenanceUpdateFragment } from '../fragments/MaintenanceUpdate';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { EdmEmailFooterPublicDataFragment } from '../fragments/EdmEmailFooterPublicData';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { DealerDisclaimersFragmentFragment } from '../fragments/DealerDisclaimersFragment';
import type { DealerIntegrationDetailsFragmentFragment } from '../fragments/DealerIntegrationDetailsFragment';
import type { ModuleInDealerSpecs_AdyenPaymentModule_Fragment, ModuleInDealerSpecs_AppointmentModule_Fragment, ModuleInDealerSpecs_AutoplayModule_Fragment, ModuleInDealerSpecs_BankModule_Fragment, ModuleInDealerSpecs_BasicSigningModule_Fragment, ModuleInDealerSpecs_CapModule_Fragment, ModuleInDealerSpecs_ConfiguratorModule_Fragment, ModuleInDealerSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleInDealerSpecs_CtsModule_Fragment, ModuleInDealerSpecs_DocusignModule_Fragment, ModuleInDealerSpecs_EventApplicationModule_Fragment, ModuleInDealerSpecs_FinderApplicationPrivateModule_Fragment, ModuleInDealerSpecs_FinderApplicationPublicModule_Fragment, ModuleInDealerSpecs_FinderVehicleManagementModule_Fragment, ModuleInDealerSpecs_FiservPaymentModule_Fragment, ModuleInDealerSpecs_GiftVoucherModule_Fragment, ModuleInDealerSpecs_InsuranceModule_Fragment, ModuleInDealerSpecs_LabelsModule_Fragment, ModuleInDealerSpecs_LaunchPadModule_Fragment, ModuleInDealerSpecs_LocalCustomerManagementModule_Fragment, ModuleInDealerSpecs_MaintenanceModule_Fragment, ModuleInDealerSpecs_MarketingModule_Fragment, ModuleInDealerSpecs_MobilityModule_Fragment, ModuleInDealerSpecs_MyInfoModule_Fragment, ModuleInDealerSpecs_NamirialSigningModule_Fragment, ModuleInDealerSpecs_OidcModule_Fragment, ModuleInDealerSpecs_PayGatePaymentModule_Fragment, ModuleInDealerSpecs_PorscheIdModule_Fragment, ModuleInDealerSpecs_PorscheMasterDataModule_Fragment, ModuleInDealerSpecs_PorschePaymentModule_Fragment, ModuleInDealerSpecs_PorscheRetainModule_Fragment, ModuleInDealerSpecs_PromoCodeModule_Fragment, ModuleInDealerSpecs_SalesControlBoardModule_Fragment, ModuleInDealerSpecs_SalesOfferModule_Fragment, ModuleInDealerSpecs_SimpleVehicleManagementModule_Fragment, ModuleInDealerSpecs_StandardApplicationModule_Fragment, ModuleInDealerSpecs_TradeInModule_Fragment, ModuleInDealerSpecs_TtbPaymentModule_Fragment, ModuleInDealerSpecs_UserlikeChatbotModule_Fragment, ModuleInDealerSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleInDealerSpecs_VisitAppointmentModule_Fragment, ModuleInDealerSpecs_WebsiteModule_Fragment, ModuleInDealerSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleInDealerSpecs';
import type { StandardApplicationModuleInDealerSpecsFragment } from '../fragments/StandardApplicationModuleInDealerSpecs';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment, ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment, ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment } from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { StandardApplicationModuleEmailContentsSpecsFragment, StandardApplicationModuleEmailContentCustomerSpecsFragment, StandardApplicationModuleEmailContentShareSubmissionSpecsFragment, StandardApplicationModuleEmailContentSpecsFragment, StandardApplicationModuleEmailContentSalesPersonSpecsFragment } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from '../fragments/DealerUploadedFileWithPreview';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { EventApplicationModuleInDealerSpecsFragment } from '../fragments/EventApplicationModuleInDealerSpecs';
import type { EventApplicationModuleEmailContentSpecsFragment } from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { ConfiguratorModuleInDealerSpecsFragment } from '../fragments/ConfiguratorModuleInDealerSpecs';
import type { DealershipSettingSpecData_DealershipMyInfoSetting_Fragment, DealershipSettingSpecData_DealershipPaymentSetting_Fragment, DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment } from '../fragments/DealershipSettingSpecData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { ConfiguratorModuleEmailContentSpecsFragment } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import type { MobilityModuleInDealerSpecsFragment } from '../fragments/MobilityModuleInDealerSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { FinderApplicationPublicModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { FinderApplicationPrivateModuleInDealerSpecsFragment } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import type { AppointmentModuleInDealerSpecsFragment } from '../fragments/AppointmentModuleInDealerSpecs';
import type { AppointmentModuleEmailContentsSpecsFragment, AppointmentModuleEmailContentCustomerSpecsFragment, AppointmentModuleEmailContentSpecsFragment, AppointmentModuleEmailContentSalesPersonSpecsFragment, AppointmentModuleEmailContentFinderReservationSpecsFragment } from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { VisitAppointmentModuleInDealerSpecsFragment } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import type { VisitAppointmentModuleEmailContentsSpecsFragment, VisitAppointmentModuleEmailContentCustomerSpecsFragment, VisitAppointmentModuleEmailContentSpecsFragment, VisitAppointmentModuleEmailContentSalesPersonSpecsFragment } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { GiftVoucherModuleInDealerSpecsFragment } from '../fragments/GiftVoucherModuleInDealerSpecs';
import type { GiftVoucherModuleEmailContentsSpecsFragment, GiftVoucherModuleEmailContentCustomerSpecsFragment, GiftVoucherModuleEmailDataFragment } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import type { LaunchPadModuleInDealerSpecsFragment } from '../fragments/LaunchPadModuleInDealerSpecs';
import type { SalesOfferModuleInDealerSpecsFragment } from '../fragments/SalesOfferModuleInDealerSpecs';
import type { SalesOfferModuleEmailContentsSpecsFragment, SalesOfferEmailContentsSpecsFragment } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { SalesControlBoardModuleInDealerSpecsFragment } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from '../fragments/DealerIntData';
import type { GiftPromoTypeDataFragment } from '../fragments/GiftPromoTypeData';
import type { DiscountPromoTypeDataFragment } from '../fragments/DiscountPromoTypeData';
import type { GiftVoucherDataFragment } from '../fragments/GiftVoucherData';
import type { ModuleSpecs_AdyenPaymentModule_Fragment, ModuleSpecs_AppointmentModule_Fragment, ModuleSpecs_AutoplayModule_Fragment, ModuleSpecs_BankModule_Fragment, ModuleSpecs_BasicSigningModule_Fragment, ModuleSpecs_CapModule_Fragment, ModuleSpecs_ConfiguratorModule_Fragment, ModuleSpecs_ConsentsAndDeclarationsModule_Fragment, ModuleSpecs_CtsModule_Fragment, ModuleSpecs_DocusignModule_Fragment, ModuleSpecs_EventApplicationModule_Fragment, ModuleSpecs_FinderApplicationPrivateModule_Fragment, ModuleSpecs_FinderApplicationPublicModule_Fragment, ModuleSpecs_FinderVehicleManagementModule_Fragment, ModuleSpecs_FiservPaymentModule_Fragment, ModuleSpecs_GiftVoucherModule_Fragment, ModuleSpecs_InsuranceModule_Fragment, ModuleSpecs_LabelsModule_Fragment, ModuleSpecs_LaunchPadModule_Fragment, ModuleSpecs_LocalCustomerManagementModule_Fragment, ModuleSpecs_MaintenanceModule_Fragment, ModuleSpecs_MarketingModule_Fragment, ModuleSpecs_MobilityModule_Fragment, ModuleSpecs_MyInfoModule_Fragment, ModuleSpecs_NamirialSigningModule_Fragment, ModuleSpecs_OidcModule_Fragment, ModuleSpecs_PayGatePaymentModule_Fragment, ModuleSpecs_PorscheIdModule_Fragment, ModuleSpecs_PorscheMasterDataModule_Fragment, ModuleSpecs_PorschePaymentModule_Fragment, ModuleSpecs_PorscheRetainModule_Fragment, ModuleSpecs_PromoCodeModule_Fragment, ModuleSpecs_SalesControlBoardModule_Fragment, ModuleSpecs_SalesOfferModule_Fragment, ModuleSpecs_SimpleVehicleManagementModule_Fragment, ModuleSpecs_StandardApplicationModule_Fragment, ModuleSpecs_TradeInModule_Fragment, ModuleSpecs_TtbPaymentModule_Fragment, ModuleSpecs_UserlikeChatbotModule_Fragment, ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleSpecs_VisitAppointmentModule_Fragment, ModuleSpecs_WebsiteModule_Fragment, ModuleSpecs_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVehicleManagementModuleSpecsFragment } from '../fragments/SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from '../fragments/LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { BankModuleSpecsFragment } from '../fragments/BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from '../fragments/BasicSigningModuleSpecs';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { StandardApplicationModuleSpecsFragment } from '../fragments/StandardApplicationModuleSpecs';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { EventApplicationModuleSpecsFragment } from '../fragments/EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from '../fragments/AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type { AdyenPaymentModuleSpecsFragment } from '../fragments/AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from '../fragments/PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from '../fragments/FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from '../fragments/PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from '../fragments/PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from '../fragments/TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from '../fragments/TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from '../fragments/MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { ConfiguratorModuleSpecsFragment } from '../fragments/ConfiguratorModuleSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from '../fragments/WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from '../fragments/WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from '../fragments/UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from '../fragments/UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from '../fragments/PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from '../fragments/MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from '../fragments/WebsiteModuleSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { LabelsModuleSpecsFragment } from '../fragments/LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from '../fragments/FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from '../fragments/FinderApplicationPublicModuleSpecs';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleSpecsFragment } from '../fragments/FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from '../fragments/AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from '../fragments/AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from '../fragments/CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from '../fragments/CtsModuleSettingData';
import type { AppointmentModuleSpecsFragment } from '../fragments/AppointmentModuleSpecs';
import type { InsuranceModuleSpecsFragment } from '../fragments/InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from '../fragments/PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from '../fragments/GiftVoucherModuleSpecs';
import type { TradeInModuleSpecsFragment } from '../fragments/TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from '../fragments/TradeInSetting';
import type { CapModuleSpecsFragment } from '../fragments/CapModuleSpecs';
import type { CapSettingSpecFragment } from '../fragments/CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from '../fragments/PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from '../fragments/PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from '../fragments/PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from '../fragments/DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from '../fragments/DocusignSettingData';
import type { LaunchPadModuleSpecsFragment } from '../fragments/LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from '../fragments/VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type { OidcModuleSpecsFragment } from '../fragments/OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from '../fragments/MarketingModuleSpecs';
import type { SalesOfferModuleSpecsFragment } from '../fragments/SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { BankIntegrationData_DbsBankIntegration_Fragment, BankIntegrationData_EmailBankIntegration_Fragment, BankIntegrationData_EnbdBankIntegration_Fragment, BankIntegrationData_HlfBankIntegration_Fragment, BankIntegrationData_HlfBankV2Integration_Fragment, BankIntegrationData_MaybankIntegration_Fragment, BankIntegrationData_UobBankIntegration_Fragment } from '../fragments/BankIntegrationData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from '../fragments/SalesControlBoardModuleSpecs';
import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_GroupApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from '../fragments/ApplicationAgreementData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from '../fragments/MarketingPlatformsAgreedSpecs';
import type { MobilityModuleGiftVoucherDataFragment } from '../fragments/MobilityModuleGiftVoucherData';
import type { DateUnitDataFragment } from '../fragments/DateUnitData';
import type { GiftVoucherDraftFlowDataFragment } from '../fragments/GiftVoucherDraftFlowData';
import type { ApplicationAdyenDepositDataFragment } from '../fragments/ApplicationAdyenDepositData';
import type { ApplicationPorscheDepositDataFragment } from '../fragments/ApplicationPorscheDepositData';
import type { ApplicationFiservDepositDataFragment } from '../fragments/ApplicationFiservDepositData';
import type { ApplicationPayGateDepositDataFragment } from '../fragments/ApplicationPayGateDepositData';
import type { ApplicationTtbDepositDataFragment } from '../fragments/ApplicationTtbDepositData';
import type { LocalVariantPublicSpecsFragment } from '../fragments/LocalVariantPublicSpecs';
import type { LocalModelPublicSpecsFragment } from '../fragments/LocalModelPublicSpecs';
import type { LocalMakePublicSpecsFragment } from '../fragments/LocalMakePublicSpecs';
import type { DealerJourneyDataFragment } from '../fragments/DealerJourneyData';
import type { MobilityStockGiftVoucherDataFragment } from '../fragments/MobilityStockGiftVoucherData';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { ApplicationDocumentDataFragment } from '../fragments/ApplicationDocumentData';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import { gql } from '@apollo/client';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { MobilityApplicationExpiredFragmentDoc } from '../fragments/MobilityApplicationExpired';
import { CustomerSpecsFragmentDoc } from '../fragments/CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from '../fragments/LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from '../fragments/LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from '../fragments/CorporateCustomerData';
import { GuarantorDataFragmentDoc } from '../fragments/GuarantorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { MobilityBookingLocationHomeDataFragmentDoc } from '../fragments/MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from '../fragments/MobilityBookingLocationPickupData';
import { ApplicationJourneyDepositFragmentDoc } from '../fragments/ApplicationJourneyDeposit';
import { PromoCodeDataFragmentDoc } from '../fragments/PromoCodeData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { DealerFragmentFragmentDoc } from '../fragments/DealerFragment';
import { CompanyContextDataFragmentDoc } from '../fragments/CompanyContextData';
import { LanguagePackContextDataFragmentDoc } from '../fragments/LanguagePackContextData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { AvailableModulesDataFragmentDoc } from '../fragments/AvailableModulesData';
import { CompanyDealerDataFragmentDoc } from '../fragments/CompanyDealerData';
import { MaintenanceUpdateFragmentDoc } from '../fragments/MaintenanceUpdate';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { EdmEmailFooterPublicDataFragmentDoc } from '../fragments/EdmEmailFooterPublicData';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { DealerDisclaimersFragmentFragmentDoc } from '../fragments/DealerDisclaimersFragment';
import { DealerIntegrationDetailsFragmentFragmentDoc } from '../fragments/DealerIntegrationDetailsFragment';
import { ModuleInDealerSpecsFragmentDoc } from '../fragments/ModuleInDealerSpecs';
import { StandardApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/StandardApplicationModuleInDealerSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { StandardApplicationModuleEmailContentsSpecsFragmentDoc, StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc, StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc, StandardApplicationModuleEmailContentSpecsFragmentDoc, StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from '../fragments/DealerUploadedFileWithPreview';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { EventApplicationModuleInDealerSpecsFragmentDoc } from '../fragments/EventApplicationModuleInDealerSpecs';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { ConfiguratorModuleInDealerSpecsFragmentDoc } from '../fragments/ConfiguratorModuleInDealerSpecs';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import { MobilityModuleInDealerSpecsFragmentDoc } from '../fragments/MobilityModuleInDealerSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { FinderApplicationPublicModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleInDealerSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { FinderApplicationPrivateModuleInDealerSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleInDealerSpecs';
import { AppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/AppointmentModuleInDealerSpecs';
import { AppointmentModuleEmailContentsSpecsFragmentDoc, AppointmentModuleEmailContentCustomerSpecsFragmentDoc, AppointmentModuleEmailContentSpecsFragmentDoc, AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc, AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc } from '../fragments/AppointmentModuleEmailContentsSpecs';
import { VisitAppointmentModuleInDealerSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleInDealerSpecs';
import { VisitAppointmentModuleEmailContentsSpecsFragmentDoc, VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc, VisitAppointmentModuleEmailContentSpecsFragmentDoc, VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { GiftVoucherModuleInDealerSpecsFragmentDoc } from '../fragments/GiftVoucherModuleInDealerSpecs';
import { GiftVoucherModuleEmailContentsSpecsFragmentDoc, GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc, GiftVoucherModuleEmailDataFragmentDoc } from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import { LaunchPadModuleInDealerSpecsFragmentDoc } from '../fragments/LaunchPadModuleInDealerSpecs';
import { SalesOfferModuleInDealerSpecsFragmentDoc } from '../fragments/SalesOfferModuleInDealerSpecs';
import { SalesOfferModuleEmailContentsSpecsFragmentDoc, SalesOfferEmailContentsSpecsFragmentDoc } from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { SalesControlBoardModuleInDealerSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleInDealerSpecs';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from '../fragments/DealerIntData';
import { GiftPromoTypeDataFragmentDoc } from '../fragments/GiftPromoTypeData';
import { DiscountPromoTypeDataFragmentDoc } from '../fragments/DiscountPromoTypeData';
import { GiftVoucherDataFragmentDoc } from '../fragments/GiftVoucherData';
import { ModuleSpecsFragmentDoc } from '../fragments/ModuleSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from '../fragments/SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { BankModuleSpecsFragmentDoc } from '../fragments/BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from '../fragments/BasicSigningModuleSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { StandardApplicationModuleSpecsFragmentDoc } from '../fragments/StandardApplicationModuleSpecs';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { EventApplicationModuleSpecsFragmentDoc } from '../fragments/EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from '../fragments/AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { AdyenPaymentModuleSpecsFragmentDoc } from '../fragments/AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from '../fragments/PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from '../fragments/FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from '../fragments/PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from '../fragments/PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from '../fragments/TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from '../fragments/TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from '../fragments/MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { ConfiguratorModuleSpecsFragmentDoc } from '../fragments/ConfiguratorModuleSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from '../fragments/WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from '../fragments/WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from '../fragments/UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from '../fragments/UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from '../fragments/PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from '../fragments/MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from '../fragments/WebsiteModuleSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { LabelsModuleSpecsFragmentDoc } from '../fragments/LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from '../fragments/FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecs';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from '../fragments/AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from '../fragments/AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from '../fragments/CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from '../fragments/CtsModuleSettingData';
import { AppointmentModuleSpecsFragmentDoc } from '../fragments/AppointmentModuleSpecs';
import { InsuranceModuleSpecsFragmentDoc } from '../fragments/InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from '../fragments/PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from '../fragments/GiftVoucherModuleSpecs';
import { TradeInModuleSpecsFragmentDoc } from '../fragments/TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from '../fragments/TradeInSetting';
import { CapModuleSpecsFragmentDoc } from '../fragments/CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from '../fragments/CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from '../fragments/PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from '../fragments/PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from '../fragments/PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from '../fragments/DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from '../fragments/DocusignSettingData';
import { LaunchPadModuleSpecsFragmentDoc } from '../fragments/LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import { OidcModuleSpecsFragmentDoc } from '../fragments/OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from '../fragments/MarketingModuleSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from '../fragments/SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleSpecs';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import { ApplicationAgreementDataFragmentDoc } from '../fragments/ApplicationAgreementData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from '../fragments/MarketingPlatformsAgreedSpecs';
import { MobilityModuleGiftVoucherDataFragmentDoc } from '../fragments/MobilityModuleGiftVoucherData';
import { DateUnitDataFragmentDoc } from '../fragments/DateUnitData';
import { GiftVoucherDraftFlowDataFragmentDoc } from '../fragments/GiftVoucherDraftFlowData';
import { ApplicationAdyenDepositDataFragmentDoc } from '../fragments/ApplicationAdyenDepositData';
import { ApplicationPorscheDepositDataFragmentDoc } from '../fragments/ApplicationPorscheDepositData';
import { ApplicationFiservDepositDataFragmentDoc } from '../fragments/ApplicationFiservDepositData';
import { ApplicationPayGateDepositDataFragmentDoc } from '../fragments/ApplicationPayGateDepositData';
import { ApplicationTtbDepositDataFragmentDoc } from '../fragments/ApplicationTtbDepositData';
import { LocalVariantPublicSpecsFragmentDoc } from '../fragments/LocalVariantPublicSpecs';
import { LocalModelPublicSpecsFragmentDoc } from '../fragments/LocalModelPublicSpecs';
import { LocalMakePublicSpecsFragmentDoc } from '../fragments/LocalMakePublicSpecs';
import { DealerJourneyDataFragmentDoc } from '../fragments/DealerJourneyData';
import { MobilityStockGiftVoucherDataFragmentDoc } from '../fragments/MobilityStockGiftVoucherData';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { ApplicationDocumentDataFragmentDoc } from '../fragments/ApplicationDocumentData';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type RetrieveLinkQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['String']['input'];
}>;


export type RetrieveLinkQuery = (
  { __typename: 'Query' }
  & { retrieveLink?: SchemaTypes.Maybe<(
    { __typename: 'AdyenRedirectionLink' }
    & Pick<SchemaTypes.AdyenRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'ApplyNewRedirectionLink' }
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: (
        { __typename: 'ConfiguratorApplication' }
        & { configurator: (
          { __typename: 'VariantConfigurator' }
          & { modelConfigurator: (
            { __typename: 'ModelConfigurator' }
            & Pick<SchemaTypes.ModelConfigurator, 'urlIdentifier'>
          ) }
        ) }
      ) | { __typename: 'EventApplication' } | (
        { __typename: 'FinderApplication' }
        & { vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & { listing: (
            { __typename: 'Listing' }
            & Pick<SchemaTypes.Listing, 'id'>
          ) }
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }> }
      ) | { __typename: 'LaunchpadApplication' } | { __typename: 'MobilityApplication' } | { __typename: 'SalesOfferApplication' } | (
        { __typename: 'StandardApplication' }
        & Pick<SchemaTypes.StandardApplication, 'vehicleId'>
      ) }
    ) }
  ) | (
    { __typename: 'CTSFinderRedirectionLink' }
    & Pick<SchemaTypes.CtsFinderRedirectionLink, 'type' | 'vehicleId'>
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: { __typename: 'ConfiguratorApplication' } | { __typename: 'EventApplication' } | (
        { __typename: 'FinderApplication' }
        & { vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & { listing: (
            { __typename: 'Listing' }
            & Pick<SchemaTypes.Listing, 'id'>
          ) }
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }> }
      ) | { __typename: 'LaunchpadApplication' } | { __typename: 'MobilityApplication' } | { __typename: 'SalesOfferApplication' } | { __typename: 'StandardApplication' } }
    ), ctsSetting: (
      { __typename: 'CtsSetting' }
      & CtsModuleSettingDataFragment
    ) }
  ) | (
    { __typename: 'ConfiguratorApplicationLink' }
    & Pick<SchemaTypes.ConfiguratorApplicationLink, 'urlIdentifier' | 'modelConfiguratorId' | 'variantConfiguratorId'>
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: (
        { __typename: 'ConfiguratorApplication' }
        & { draftFlow: (
          { __typename: 'StandardApplicationDraftFlow' }
          & Pick<SchemaTypes.StandardApplicationDraftFlow, 'isReceived'>
        ) }
        & ApplicationStageData_ConfiguratorApplication_Fragment
      ) | (
        { __typename: 'EventApplication' }
        & ApplicationStageData_EventApplication_Fragment
      ) | (
        { __typename: 'FinderApplication' }
        & ApplicationStageData_FinderApplication_Fragment
      ) | (
        { __typename: 'LaunchpadApplication' }
        & ApplicationStageData_LaunchpadApplication_Fragment
      ) | (
        { __typename: 'MobilityApplication' }
        & ApplicationStageData_MobilityApplication_Fragment
      ) | (
        { __typename: 'SalesOfferApplication' }
        & ApplicationStageData_SalesOfferApplication_Fragment
      ) | (
        { __typename: 'StandardApplication' }
        & ApplicationStageData_StandardApplication_Fragment
      ) }
    ) }
  ) | (
    { __typename: 'CreateNewUserLink' }
    & Pick<SchemaTypes.CreateNewUserLink, 'token' | 'verified'>
  ) | (
    { __typename: 'EventApplicationLink' }
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
    ) }
  ) | (
    { __typename: 'FinderApplicationLink' }
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: { __typename: 'ConfiguratorApplication' } | { __typename: 'EventApplication' } | (
        { __typename: 'FinderApplication' }
        & { vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & { listing: (
            { __typename: 'Listing' }
            & Pick<SchemaTypes.Listing, 'id'>
          ) }
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }> }
      ) | { __typename: 'LaunchpadApplication' } | { __typename: 'MobilityApplication' } | { __typename: 'SalesOfferApplication' } | { __typename: 'StandardApplication' } }
    ) }
  ) | (
    { __typename: 'FiservPaymentRedirectionLink' }
    & Pick<SchemaTypes.FiservPaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'GiftVoucherAdyenRedirectionLink' }
    & Pick<SchemaTypes.GiftVoucherAdyenRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'GiftVoucherFiservPaymentRedirectionLink' }
    & Pick<SchemaTypes.GiftVoucherFiservPaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'GiftVoucherPayGatePaymentRedirectionLink' }
    & Pick<SchemaTypes.GiftVoucherPayGatePaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'GiftVoucherPorschePaymentRedirectionLink' }
    & Pick<SchemaTypes.GiftVoucherPorschePaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'GiftVoucherTtbPaymentRedirectionLink' }
    & Pick<SchemaTypes.GiftVoucherTtbPaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'MobilityApplicationAmendmentLink' }
    & Pick<SchemaTypes.MobilityApplicationAmendmentLink, 'token' | 'path'>
    & { application: { __typename: 'ConfiguratorApplication' } | { __typename: 'EventApplication' } | { __typename: 'FinderApplication' } | { __typename: 'LaunchpadApplication' } | (
      { __typename: 'MobilityApplication' }
      & MobilityApplicationExpiredFragment
    ) | { __typename: 'SalesOfferApplication' } | { __typename: 'StandardApplication' } }
  ) | (
    { __typename: 'MobilityApplicationCancellationLink' }
    & Pick<SchemaTypes.MobilityApplicationCancellationLink, 'token' | 'path'>
    & { application: { __typename: 'ConfiguratorApplication' } | { __typename: 'EventApplication' } | { __typename: 'FinderApplication' } | { __typename: 'LaunchpadApplication' } | (
      { __typename: 'MobilityApplication' }
      & MobilityApplicationExpiredFragment
    ) | { __typename: 'SalesOfferApplication' } | { __typename: 'StandardApplication' } }
  ) | (
    { __typename: 'MyInfoCallbackLink' }
    & Pick<SchemaTypes.MyInfoCallbackLink, 'linkId'>
    & { applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: (
        { __typename: 'ConfiguratorApplication' }
        & Pick<SchemaTypes.ConfiguratorApplication, 'withCustomerDevice'>
      ) | (
        { __typename: 'EventApplication' }
        & Pick<SchemaTypes.EventApplication, 'withCustomerDevice'>
      ) | (
        { __typename: 'FinderApplication' }
        & Pick<SchemaTypes.FinderApplication, 'withCustomerDevice'>
        & { vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & { listing: (
            { __typename: 'Listing' }
            & Pick<SchemaTypes.Listing, 'id'>
          ) }
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }> }
      ) | { __typename: 'LaunchpadApplication' } | { __typename: 'MobilityApplication' } | { __typename: 'SalesOfferApplication' } | (
        { __typename: 'StandardApplication' }
        & Pick<SchemaTypes.StandardApplication, 'withCustomerDevice'>
      ) }
    ), router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ) }
  ) | (
    { __typename: 'NamirialSigningLink' }
    & Pick<SchemaTypes.NamirialSigningLink, 'token' | 'path'>
  ) | (
    { __typename: 'PayGatePaymentRedirectionLink' }
    & Pick<SchemaTypes.PayGatePaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'PorscheIdCallbackLink' }
    & Pick<SchemaTypes.PorscheIdCallbackLink, 'linkId'>
    & { applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: (
        { __typename: 'ConfiguratorApplication' }
        & Pick<SchemaTypes.ConfiguratorApplication, 'withCustomerDevice'>
      ) | (
        { __typename: 'EventApplication' }
        & Pick<SchemaTypes.EventApplication, 'withCustomerDevice'>
      ) | (
        { __typename: 'FinderApplication' }
        & Pick<SchemaTypes.FinderApplication, 'withCustomerDevice'>
        & { vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & { listing: (
            { __typename: 'Listing' }
            & Pick<SchemaTypes.Listing, 'id'>
          ) }
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }> }
      ) | { __typename: 'LaunchpadApplication' } | { __typename: 'MobilityApplication' } | { __typename: 'SalesOfferApplication' } | { __typename: 'StandardApplication' } }
    ), router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ) }
  ) | (
    { __typename: 'PorschePaymentRedirectionLink' }
    & Pick<SchemaTypes.PorschePaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'ProceedWithCustomerLink' }
    & Pick<SchemaTypes.ProceedWithCustomerLink, 'secret' | 'applicationKind'>
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ) }
  ) | (
    { __typename: 'ResetPasswordLink' }
    & Pick<SchemaTypes.ResetPasswordLink, 'token' | 'verified' | 'email'>
  ) | (
    { __typename: 'SalesOfferNamirialSigningLink' }
    & Pick<SchemaTypes.SalesOfferNamirialSigningLink, 'token' | 'isVsaSigningForSalesManager'>
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ) }
  ) | (
    { __typename: 'SendSalesOfferLink' }
    & Pick<SchemaTypes.SendSalesOfferLink, 'linkId' | 'secret' | 'featureKinds' | 'isVsaSigningForSalesManager'>
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ) }
  ) | (
    { __typename: 'StandardApplicationLink' }
    & { router: (
      { __typename: 'Router' }
      & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
    ), endpoint: (
      { __typename: 'ApplicationListEndpoint' }
      & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'ConfiguratorApplicationEntrypoint' }
      & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'CustomerListEndpoint' }
      & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyPrivatePageEndpoint' }
      & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'DummyWelcomePageEndpoint' }
      & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'EventApplicationEntrypoint' }
      & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'FinderApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LaunchPadApplicationEntrypoint' }
      & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'LeadListEndpoint' }
      & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'MobilityApplicationEntrypoint' }
      & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'StandardApplicationPublicAccessEntrypoint' }
      & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
    ) | (
      { __typename: 'WebPageEndpoint' }
      & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
    ), applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
    ) }
  ) | (
    { __typename: 'TestDriveProcessRedirectionLink' }
    & { applicationJourney: (
      { __typename: 'ApplicationJourney' }
      & Pick<SchemaTypes.ApplicationJourney, 'token'>
      & { application: (
        { __typename: 'ConfiguratorApplication' }
        & { router?: SchemaTypes.Maybe<(
          { __typename: 'Router' }
          & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
        )>, endpoint?: SchemaTypes.Maybe<(
          { __typename: 'ApplicationListEndpoint' }
          & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'ConfiguratorApplicationEntrypoint' }
          & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'CustomerListEndpoint' }
          & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyPrivatePageEndpoint' }
          & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyWelcomePageEndpoint' }
          & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'EventApplicationEntrypoint' }
          & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LaunchPadApplicationEntrypoint' }
          & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LeadListEndpoint' }
          & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'MobilityApplicationEntrypoint' }
          & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'WebPageEndpoint' }
          & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
        )>, versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'EventApplication' }
        & { event: (
          { __typename: 'Event' }
          & Pick<SchemaTypes.Event, 'urlSlug'>
        ), router?: SchemaTypes.Maybe<(
          { __typename: 'Router' }
          & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
        )>, endpoint?: SchemaTypes.Maybe<(
          { __typename: 'ApplicationListEndpoint' }
          & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'ConfiguratorApplicationEntrypoint' }
          & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'CustomerListEndpoint' }
          & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyPrivatePageEndpoint' }
          & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyWelcomePageEndpoint' }
          & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'EventApplicationEntrypoint' }
          & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LaunchPadApplicationEntrypoint' }
          & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LeadListEndpoint' }
          & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'MobilityApplicationEntrypoint' }
          & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'WebPageEndpoint' }
          & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
        )>, versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'FinderApplication' }
        & { router?: SchemaTypes.Maybe<(
          { __typename: 'Router' }
          & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
        )>, endpoint?: SchemaTypes.Maybe<(
          { __typename: 'ApplicationListEndpoint' }
          & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'ConfiguratorApplicationEntrypoint' }
          & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'CustomerListEndpoint' }
          & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyPrivatePageEndpoint' }
          & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyWelcomePageEndpoint' }
          & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'EventApplicationEntrypoint' }
          & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LaunchPadApplicationEntrypoint' }
          & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LeadListEndpoint' }
          & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'MobilityApplicationEntrypoint' }
          & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'WebPageEndpoint' }
          & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
        )>, vehicle?: SchemaTypes.Maybe<(
          { __typename: 'FinderVehicle' }
          & FinderVehicleSpecsFragment
        ) | { __typename: 'LocalMake' } | { __typename: 'LocalModel' } | { __typename: 'LocalVariant' }>, versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'LaunchpadApplication' }
        & { router?: SchemaTypes.Maybe<(
          { __typename: 'Router' }
          & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
        )>, endpoint?: SchemaTypes.Maybe<(
          { __typename: 'ApplicationListEndpoint' }
          & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'ConfiguratorApplicationEntrypoint' }
          & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'CustomerListEndpoint' }
          & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyPrivatePageEndpoint' }
          & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyWelcomePageEndpoint' }
          & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'EventApplicationEntrypoint' }
          & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LaunchPadApplicationEntrypoint' }
          & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LeadListEndpoint' }
          & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'MobilityApplicationEntrypoint' }
          & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'WebPageEndpoint' }
          & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
        )>, versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'MobilityApplication' }
        & { versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'SalesOfferApplication' }
        & { versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) | (
        { __typename: 'StandardApplication' }
        & { router?: SchemaTypes.Maybe<(
          { __typename: 'Router' }
          & Pick<SchemaTypes.Router, 'id' | 'pathname' | 'hostname'>
        )>, endpoint?: SchemaTypes.Maybe<(
          { __typename: 'ApplicationListEndpoint' }
          & Pick<SchemaTypes.ApplicationListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'ConfiguratorApplicationEntrypoint' }
          & Pick<SchemaTypes.ConfiguratorApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'CustomerListEndpoint' }
          & Pick<SchemaTypes.CustomerListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyPrivatePageEndpoint' }
          & Pick<SchemaTypes.DummyPrivatePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'DummyWelcomePageEndpoint' }
          & Pick<SchemaTypes.DummyWelcomePageEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'EventApplicationEntrypoint' }
          & Pick<SchemaTypes.EventApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'FinderApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.FinderApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LaunchPadApplicationEntrypoint' }
          & Pick<SchemaTypes.LaunchPadApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'LeadListEndpoint' }
          & Pick<SchemaTypes.LeadListEndpoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'MobilityApplicationEntrypoint' }
          & Pick<SchemaTypes.MobilityApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'StandardApplicationPublicAccessEntrypoint' }
          & Pick<SchemaTypes.StandardApplicationPublicAccessEntrypoint, 'id' | 'pathname'>
        ) | (
          { __typename: 'WebPageEndpoint' }
          & Pick<SchemaTypes.WebPageEndpoint, 'id' | 'pathname'>
        )>, versioning: (
          { __typename: 'AdvancedVersioning' }
          & Pick<SchemaTypes.AdvancedVersioning, 'suiteId'>
        ) }
      ) }
    ) }
  ) | (
    { __typename: 'TtbPaymentRedirectionLink' }
    & Pick<SchemaTypes.TtbPaymentRedirectionLink, 'token' | 'path'>
  ) | (
    { __typename: 'VerifyEmailUpdateLink' }
    & Pick<SchemaTypes.VerifyEmailUpdateLink, 'token' | 'email' | 'userId'>
  )> }
);


export const RetrieveLinkDocument = /*#__PURE__*/ gql`
    query retrieveLink($id: String!) {
  retrieveLink(id: $id) {
    __typename
    ... on ResetPasswordLink {
      token
      verified
      email
    }
    ... on CreateNewUserLink {
      token
      verified
    }
    ... on MyInfoCallbackLink {
      linkId
      applicationJourney {
        token
        application {
          ... on StandardApplication {
            withCustomerDevice
          }
          ... on ConfiguratorApplication {
            withCustomerDevice
          }
          ... on EventApplication {
            withCustomerDevice
          }
          ... on FinderApplication {
            vehicle {
              ... on FinderVehicle {
                listing {
                  id
                }
              }
            }
            withCustomerDevice
          }
        }
      }
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
    }
    ... on NamirialSigningLink {
      token
      path
    }
    ... on AdyenRedirectionLink {
      token
      path
    }
    ... on ConfiguratorApplicationLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      applicationJourney {
        token
        application {
          ...ApplicationStageData
          ... on ConfiguratorApplication {
            draftFlow {
              isReceived
            }
          }
        }
      }
      urlIdentifier
      modelConfiguratorId
      variantConfiguratorId
    }
    ... on StandardApplicationLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      applicationJourney {
        token
      }
    }
    ... on EventApplicationLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      applicationJourney {
        token
      }
    }
    ... on FinderApplicationLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      applicationJourney {
        token
        application {
          ... on FinderApplication {
            vehicle {
              ... on FinderVehicle {
                listing {
                  id
                }
              }
            }
          }
        }
      }
    }
    ... on VerifyEmailUpdateLink {
      token
      email
      userId
    }
    ... on ProceedWithCustomerLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      secret
      applicationKind
    }
    ... on PorschePaymentRedirectionLink {
      token
      path
    }
    ... on FiservPaymentRedirectionLink {
      token
      path
    }
    ... on PayGatePaymentRedirectionLink {
      token
      path
    }
    ... on TtbPaymentRedirectionLink {
      token
      path
    }
    ... on MobilityApplicationAmendmentLink {
      token
      path
      application {
        ...MobilityApplicationExpired
      }
    }
    ... on MobilityApplicationCancellationLink {
      token
      path
      application {
        ...MobilityApplicationExpired
      }
    }
    ... on TestDriveProcessRedirectionLink {
      applicationJourney {
        token
        application {
          versioning {
            suiteId
          }
          ... on EventApplication {
            event {
              urlSlug
            }
            router {
              id
              pathname
              hostname
            }
            endpoint {
              id
              pathname
            }
          }
          ... on FinderApplication {
            router {
              id
              pathname
              hostname
            }
            endpoint {
              id
              pathname
            }
            vehicle {
              ...FinderVehicleSpecs
            }
          }
          ... on StandardApplication {
            router {
              id
              pathname
              hostname
            }
            endpoint {
              id
              pathname
            }
          }
          ... on LaunchpadApplication {
            router {
              id
              pathname
              hostname
            }
            endpoint {
              id
              pathname
            }
          }
          ... on ConfiguratorApplication {
            router {
              id
              pathname
              hostname
            }
            endpoint {
              id
              pathname
            }
          }
        }
      }
    }
    ... on CTSFinderRedirectionLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      type
      vehicleId
      applicationJourney {
        token
        application {
          ... on FinderApplication {
            vehicle {
              ... on FinderVehicle {
                listing {
                  id
                }
              }
            }
          }
        }
      }
      ctsSetting {
        ...CtsModuleSettingData
      }
    }
    ... on GiftVoucherAdyenRedirectionLink {
      token
      path
    }
    ... on GiftVoucherTtbPaymentRedirectionLink {
      token
      path
    }
    ... on GiftVoucherPorschePaymentRedirectionLink {
      token
      path
    }
    ... on GiftVoucherPayGatePaymentRedirectionLink {
      token
      path
    }
    ... on GiftVoucherFiservPaymentRedirectionLink {
      token
      path
    }
    ... on ApplyNewRedirectionLink {
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      applicationJourney {
        token
        application {
          ... on StandardApplication {
            vehicleId
          }
          ... on FinderApplication {
            vehicle {
              ... on FinderVehicle {
                listing {
                  id
                }
              }
            }
          }
          ... on ConfiguratorApplication {
            configurator {
              modelConfigurator {
                ... on ModelConfigurator {
                  urlIdentifier
                }
              }
            }
          }
        }
      }
    }
    ... on PorscheIdCallbackLink {
      linkId
      applicationJourney {
        token
        application {
          ... on ConfiguratorApplication {
            withCustomerDevice
          }
          ... on EventApplication {
            withCustomerDevice
          }
          ... on FinderApplication {
            vehicle {
              ... on FinderVehicle {
                listing {
                  id
                }
              }
            }
            withCustomerDevice
          }
        }
      }
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
    }
    ... on SendSalesOfferLink {
      linkId
      secret
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      featureKinds
      isVsaSigningForSalesManager
    }
    ... on SalesOfferNamirialSigningLink {
      token
      router {
        id
        pathname
        hostname
      }
      endpoint {
        id
        pathname
      }
      isVsaSigningForSalesManager
    }
  }
}
    ${ApplicationStageDataFragmentDoc}
${MobilityApplicationExpiredFragmentDoc}
${CustomerSpecsFragmentDoc}
${LocalCustomerDataFragmentDoc}
${LocalCustomerFieldDataFragmentDoc}
${CorporateCustomerDataFragmentDoc}
${GuarantorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${MobilityBookingLocationHomeDataFragmentDoc}
${MobilityBookingLocationPickupDataFragmentDoc}
${ApplicationJourneyDepositFragmentDoc}
${PromoCodeDataFragmentDoc}
${PeriodDataFragmentDoc}
${DealerFragmentFragmentDoc}
${CompanyContextDataFragmentDoc}
${LanguagePackContextDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${AvailableModulesDataFragmentDoc}
${CompanyDealerDataFragmentDoc}
${MaintenanceUpdateFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${EdmEmailFooterPublicDataFragmentDoc}
${EdmSocialMediaDataFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${DealerDisclaimersFragmentFragmentDoc}
${DealerIntegrationDetailsFragmentFragmentDoc}
${ModuleInDealerSpecsFragmentDoc}
${StandardApplicationModuleInDealerSpecsFragmentDoc}
${DealerPriceDisclaimerDataFragmentDoc}
${ApplicationMarketTypeFragmentFragmentDoc}
${DealerMarketDataFragmentDoc}
${BankDealerMarketDataFragmentDoc}
${NzFeesDealerMarketDataFragmentDoc}
${DealerVehiclesSpecsFragmentDoc}
${DealerFinanceProductsSpecsFragmentDoc}
${FinanceProductListDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${DealerInsuranceProductsSpecsFragmentDoc}
${InsuranceProductListDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${StandardApplicationModuleEmailContentsSpecsFragmentDoc}
${StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc}
${StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerUploadedFileWithPreviewDataFragmentDoc}
${DealerBooleanSettingDataFragmentDoc}
${StandardApplicationModuleEmailContentSpecsFragmentDoc}
${StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc}
${EventApplicationModuleInDealerSpecsFragmentDoc}
${EventApplicationModuleEmailContentSpecsFragmentDoc}
${TranslatedTextDataFragmentDoc}
${ConfiguratorModuleInDealerSpecsFragmentDoc}
${DealershipSettingSpecDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${ConfiguratorModuleEmailContentSpecsFragmentDoc}
${MobilityModuleInDealerSpecsFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MobilityLocationDataFragmentDoc}
${FinderApplicationPublicModuleInDealerSpecsFragmentDoc}
${FinderApplicationModuleEmailContentSpecsFragmentDoc}
${FinderApplicationPrivateModuleInDealerSpecsFragmentDoc}
${AppointmentModuleInDealerSpecsFragmentDoc}
${AppointmentModuleEmailContentsSpecsFragmentDoc}
${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${AppointmentModuleEmailContentSpecsFragmentDoc}
${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
${VisitAppointmentModuleInDealerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
${GiftVoucherModuleInDealerSpecsFragmentDoc}
${GiftVoucherModuleEmailContentsSpecsFragmentDoc}
${GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc}
${GiftVoucherModuleEmailDataFragmentDoc}
${LaunchPadModuleInDealerSpecsFragmentDoc}
${SalesOfferModuleInDealerSpecsFragmentDoc}
${SalesOfferModuleEmailContentsSpecsFragmentDoc}
${SalesOfferEmailContentsSpecsFragmentDoc}
${SalesControlBoardModuleInDealerSpecsFragmentDoc}
${DealerIntDataFragmentDoc}
${DealerFloatDataFragmentDoc}
${DealerObjectIdDataFragmentDoc}
${GiftPromoTypeDataFragmentDoc}
${DiscountPromoTypeDataFragmentDoc}
${GiftVoucherDataFragmentDoc}
${ModuleSpecsFragmentDoc}
${ConsentsAndDeclarationsModuleSpecsFragmentDoc}
${SimpleVehicleManagementModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${LocalCustomerManagementModuleSpecsFragmentDoc}
${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
${KycExtraSettingsSpecsFragmentDoc}
${KycPresetsSpecFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${BankModuleSpecsFragmentDoc}
${BasicSigningModuleSpecsFragmentDoc}
${NamirialSigningModuleSpecsFragmentDoc}
${NamirialSettingsSpecFragmentDoc}
${StandardApplicationModuleSpecsFragmentDoc}
${DepositAmountDataFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${FlexibleDiscountDataFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${EventApplicationModuleSpecsFragmentDoc}
${AppointmentModuleOnEventModuleDataFragmentDoc}
${AppointmentTimeSlotDataFragmentDoc}
${AdyenPaymentModuleSpecsFragmentDoc}
${AdyenPaymentSettingsSpecFragmentDoc}
${PorschePaymentModuleSpecsFragmentDoc}
${PorschePaymentSettingsSpecFragmentDoc}
${FiservPaymentModuleSpecsFragmentDoc}
${FiservPaymentSettingsSpecFragmentDoc}
${PayGatePaymentModuleSpecsFragmentDoc}
${PayGatePaymentSettingsSpecFragmentDoc}
${TtbPaymentModuleSpecsFragmentDoc}
${TtbPaymentSettingsSpecFragmentDoc}
${MyInfoModuleSpecsFragmentDoc}
${MyInfoSettingSpecFragmentDoc}
${ConfiguratorModuleSpecsFragmentDoc}
${WhatsappLiveChatModuleSpecsFragmentDoc}
${WhatsappLiveChatSettingsSpecFragmentDoc}
${UserlikeChatbotModuleSpecsFragmentDoc}
${UserlikeChatbotSettingsSpecFragmentDoc}
${PromoCodeModuleSpecsFragmentDoc}
${MaintenanceModuleSpecsFragmentDoc}
${WebsiteModuleSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${LabelsModuleSpecsFragmentDoc}
${FinderVehicleManagementModuleSpecsFragmentDoc}
${FinderApplicationPublicModuleSpecsFragmentDoc}
${ModuleDisclaimersDataFragmentDoc}
${FinderApplicationPrivateModuleSpecsFragmentDoc}
${AutoplayModuleSpecsFragmentDoc}
${AutoplaySettingSpecsFragmentDoc}
${CtsModuleSpecsFragmentDoc}
${CtsModuleSettingDataFragmentDoc}
${AppointmentModuleSpecsFragmentDoc}
${InsuranceModuleSpecsFragmentDoc}
${PorscheMasterDataModuleSpecsFragmentDoc}
${GiftVoucherModuleSpecsFragmentDoc}
${TradeInModuleSpecsFragmentDoc}
${TradeInSettingSpecFragmentDoc}
${CapModuleSpecsFragmentDoc}
${CapSettingSpecFragmentDoc}
${PorscheIdModuleSpecsFragmentDoc}
${PorscheIdSettingSpecFragmentDoc}
${PorscheRetainModuleSpecsFragmentDoc}
${DocusignModuleSpecsFragmentDoc}
${DocusignSettingDataFragmentDoc}
${LaunchPadModuleSpecsFragmentDoc}
${VisitAppointmentModuleSpecsFragmentDoc}
${TimeSlotDataFragmentDoc}
${OidcModuleSpecsFragmentDoc}
${MarketingModuleSpecsFragmentDoc}
${SalesOfferModuleSpecsFragmentDoc}
${BankDetailsDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${BankIntegrationDataFragmentDoc}
${UploadFileFormDataFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc}
${SalesControlBoardModuleSpecsFragmentDoc}
${KycFieldSpecsFragmentDoc}
${ApplicationAgreementDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}
${MarketingPlatformsAgreedSpecsFragmentDoc}
${MobilityModuleGiftVoucherDataFragmentDoc}
${DateUnitDataFragmentDoc}
${GiftVoucherDraftFlowDataFragmentDoc}
${ApplicationAdyenDepositDataFragmentDoc}
${ApplicationPorscheDepositDataFragmentDoc}
${ApplicationFiservDepositDataFragmentDoc}
${ApplicationPayGateDepositDataFragmentDoc}
${ApplicationTtbDepositDataFragmentDoc}
${LocalVariantPublicSpecsFragmentDoc}
${LocalModelPublicSpecsFragmentDoc}
${LocalMakePublicSpecsFragmentDoc}
${DealerJourneyDataFragmentDoc}
${MobilityStockGiftVoucherDataFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${ApplicationDocumentDataFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}`;

/**
 * __useRetrieveLinkQuery__
 *
 * To run a query within a React component, call `useRetrieveLinkQuery` and pass it any options that fit your needs.
 * When your component renders, `useRetrieveLinkQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRetrieveLinkQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useRetrieveLinkQuery(baseOptions: Apollo.QueryHookOptions<RetrieveLinkQuery, RetrieveLinkQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RetrieveLinkQuery, RetrieveLinkQueryVariables>(RetrieveLinkDocument, options);
      }
export function useRetrieveLinkLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RetrieveLinkQuery, RetrieveLinkQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RetrieveLinkQuery, RetrieveLinkQueryVariables>(RetrieveLinkDocument, options);
        }
export type RetrieveLinkQueryHookResult = ReturnType<typeof useRetrieveLinkQuery>;
export type RetrieveLinkLazyQueryHookResult = ReturnType<typeof useRetrieveLinkLazyQuery>;
export type RetrieveLinkQueryResult = Apollo.QueryResult<RetrieveLinkQuery, RetrieveLinkQueryVariables>;