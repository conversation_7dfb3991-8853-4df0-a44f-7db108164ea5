import { useApolloClient } from '@apollo/client';
import { Col, Space } from 'antd';
import { Formik, useFormikContext } from 'formik';
import { isValidNumber, parsePhoneNumberFromString } from 'libphonenumber-js';
import { flatten, isEmpty } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../api/fragments/ApplicationAgreementData';
import { KycFieldSpecsFragment } from '../../../api/fragments/KYCFieldSpecs';
import {
    CreateNewContactFromBpDocument,
    type CreateNewContactFromBpMutation,
    type CreateNewContactFromBpMutationVariables,
} from '../../../api/mutations/createNewContactFromBp';
import { usePrefetchAgreementsForContactQuery } from '../../../api/queries/prefetchAgreementsForContact';
import { usePrefetchKycFieldsForContactQuery } from '../../../api/queries/prefetchKYCFieldsForContact';
import {
    CustomerKind,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
    LocalCustomerManagementModule,
    CapValuesInput,
    TradeInVehiclePayload,
} from '../../../api/types';
import { getApplicantKyc } from '../../../pages/portal/StandardApplicationEntrypoint/KYCPage/getKyc';
import { getApplicantAgreements } from '../../../pages/shared/CIPage/ConsentAndDeclarations/getAgreements';
import {
    AgreementValues,
    getAgreementValues,
    mergeAgreementValues,
} from '../../../pages/shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import getKYCDataFromCap, {
    getCurrentVehicleFromCap,
} from '../../../pages/shared/JourneyPage/C@P/SearchCapCustomerOnKYC/getKYCDataFromCap';
import CustomerDetails from '../../../pages/shared/JourneyPage/CustomerDetails';
import refineCustomerValues from '../../../pages/shared/refineCustomerValues';
import { useThemeComponents } from '../../../themes/hooks';
import { getInitialValues, prepareKYCFieldPayload, KYCPresetFormFields } from '../../../utilities/kycPresets';
import isKYCPresetHasCurrentVehicleFields from '../../../utilities/kycPresets/isKYCPresetHasCurrentVehicleFields';
import { type UploadDocumentProp } from '../../../utilities/kycPresets/shared';
import useHandleError from '../../../utilities/useHandleError';
import useValidator from '../../../utilities/useValidator';
import { emailRegex } from '../../../utilities/validators';
import FormAutoTouch from '../../FormAutoTouch';
import LoadingElement from '../../LoadingElement';
import { SearchCapCustomerKYCProps } from '../../cap/searchCustomersAndLeads/types';
import { type StateAndDispatch } from '../../cap/searchCustomersAndLeads/useReducer';
import { hasSearchedCustomerValues } from '../../cap/searchCustomersAndLeads/utils';
import { useLanguage } from '../../contexts/LanguageContextManager';
import Form from '../../fields/Form';
import { defaultColSpan } from '../shared';
import ConsentsAndDeclarations from './ConsentAndDeclarations';
import useNewContactValidator from './useNewContactValidator';

type ApplicationKYCAndAgreement = {
    agreements: AgreementValues;
    customer: { fields: KYCPresetFormFields };
    tradeInVehicle: TradeInVehiclePayload[];
    capValues?: CapValuesInput;
};

const Container = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 24px;
`;

type InnerProps = StateAndDispatch & {
    kycError: boolean;
    agreement: { values: AgreementValues; data: ApplicationAgreementDataFragment[] };
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    kycState: KycFieldSpecsFragment[];
};
const Inner = ({ state, dispatch, agreement, kycExtraSettings, kycState, kycError }: InnerProps) => {
    const { selectedBusinessPartner, searchedCustomer } = state;
    const [fieldHasfilledByCap, setFieldHasFilledByCap] = useState(false);
    const [isPrefilledWithSearchTerm, setIsPrefilledWithSearchTerm] = useState(false);

    const { values, resetForm, initialValues, setFieldValue, errors, isValid, validateForm, handleSubmit } =
        useFormikContext<ApplicationKYCAndAgreement>();

    const resetFormHandler = useCallback(() => {
        resetForm({
            values: {
                ...initialValues,
                customer: { fields: getInitialValues([], kycState) },
                agreements: { ...values.agreements },
            },
        });
    }, [initialValues, kycState, resetForm, values.agreements]);

    useEffect(() => {
        if (!kycError && !fieldHasfilledByCap && selectedBusinessPartner) {
            if (isKYCPresetHasCurrentVehicleFields(kycState)) {
                setFieldValue(
                    'tradeInVehicle.0',
                    getCurrentVehicleFromCap(selectedBusinessPartner, values.tradeInVehicle)
                );
            }
            setFieldValue('customer.fields', getKYCDataFromCap(selectedBusinessPartner, kycState, values.customer));
            setFieldValue('capValues', {
                businessPartnerGuid: selectedBusinessPartner.businessPartnerGuid,
                businessPartnerId: selectedBusinessPartner.businessPartnerId,
                salesPersonId: selectedBusinessPartner.responsibleSalesPersonId,
            });
            setFieldHasFilledByCap(true);
        }
    }, [
        fieldHasfilledByCap,
        kycError,
        kycState,
        selectedBusinessPartner,
        setFieldValue,
        validateForm,
        values.customer,
        values.tradeInVehicle,
    ]);

    // Helper function to map searchedCustomer to KYC fields
    const getKYCDataFromSearchedCustomer = useCallback((searchedCustomer, kycFields, customerFields) => {
        const { fields } = customerFields;

        // Complete mapping based on SearchedCustomer type and existing patterns in getKYCDataFromCap.tsx
        const searchedCustomerToKYCMapping = {
            email: LocalCustomerFieldKey.Email,
            phone: LocalCustomerFieldKey.Phone,
            firstName: LocalCustomerFieldKey.FirstName,
            lastName: LocalCustomerFieldKey.LastName,
            firstNameJapan: LocalCustomerFieldKey.FirstNameJapan,
            lastNameJapan: LocalCustomerFieldKey.LastNameJapan,
            vin: LocalCustomerFieldKey.CurrentVehicleVin,
        };

        const hasKycField = key => kycFields.some(kycField => kycField.key === key);

        Object.keys(searchedCustomer).forEach(key => {
            const kycField = searchedCustomerToKYCMapping[key];
            const value = searchedCustomer[key];

            // Only process if value exists and the corresponding KYC field is available
            if (value && kycField && hasKycField(kycField)) {
                switch (kycField) {
                    case LocalCustomerFieldKey.Phone: {
                        // Since value is always a valid number, extract prefix and phone number
                        try {
                            const phoneNumber = parsePhoneNumberFromString(value as string);

                            if (phoneNumber && isValidNumber(value as string)) {
                                fields[kycField] = {
                                    value: {
                                        prefix: phoneNumber.countryCallingCode
                                            ? Number(phoneNumber.countryCallingCode)
                                            : undefined,
                                        value: phoneNumber.nationalNumber,
                                    },
                                    source: LocalCustomerFieldSource.UserInput,
                                };
                            }
                        } catch (error) {
                            // Fallback: if parsing fails but we know it's a valid number
                            // Try to extract manually or preserve existing prefix
                            if (fields[LocalCustomerFieldKey.Phone]?.value?.prefix) {
                                fields[kycField] = {
                                    value: {
                                        prefix: fields[LocalCustomerFieldKey.Phone].value.prefix,
                                        value: value as string,
                                    },
                                    source: LocalCustomerFieldSource.UserInput,
                                };
                            }
                        }
                        break;
                    }

                    case LocalCustomerFieldKey.Email: {
                        // Only set email if it's valid using emailRegex
                        if (emailRegex.test(value)) {
                            fields[kycField] = {
                                value,
                                source: LocalCustomerFieldSource.UserInput,
                            };
                        }
                        break;
                    }

                    default: {
                        // For all other fields, set the value directly
                        fields[kycField] = {
                            value,
                            source: LocalCustomerFieldSource.UserInput,
                        };
                    }
                }
            }
        });

        return fields;
    }, []);

    useEffect(() => {
        // clicking "create new" button => auto prefill all available searchedCustomer fields
        if (!selectedBusinessPartner && hasSearchedCustomerValues(searchedCustomer) && !isPrefilledWithSearchTerm) {
            const updatedFields = getKYCDataFromSearchedCustomer(searchedCustomer, kycState, values.customer);
            setFieldValue('customer.fields', updatedFields);
            if (!isEmpty(updatedFields)) {
                setIsPrefilledWithSearchTerm(true);
            }
        }
    }, [
        isPrefilledWithSearchTerm,
        kycState,
        searchedCustomer,
        selectedBusinessPartner,
        setFieldValue,
        values.customer,
        values.customer.fields?.Phone?.value?.prefix,
        getKYCDataFromSearchedCustomer,
    ]);

    useEffect(() => {
        validateForm();
        dispatch({ type: 'setIsKycValid', isKycValid: isValid });
    }, [dispatch, errors, isValid, validateForm, values]);

    // This function below is to automatically submit the kyc if the value from C@P has fullfilled
    // the available kyc fields
    useEffect(() => {
        // If has consent, do not automatically submit the kyc
        const hasAgreement = agreement?.data?.length;
        const fieldKeys = kycState.map(kyc => kyc.key);
        const filledField = Object.keys(values.customer.fields)
            .map(key => {
                const { value } = values.customer.fields[key];

                if (!value) {
                    return false;
                }

                return typeof value === 'string' ? value !== '' : true;
            })
            .filter(Boolean);

        if (isValid && isEmpty(errors) && !hasAgreement && fieldKeys.length === filledField.length) {
            handleSubmit();
        }
    }, [agreement, errors, handleSubmit, isValid, kycState, values]);

    return (
        <Form data-cy="newContactId" id="applicantForm" name="applicantForm" onSubmitCapture={handleSubmit} noValidate>
            <FormAutoTouch />
            <Container>
                <Space direction="vertical" size={24} style={{ width: '100%' }}>
                    <CustomerDetails
                        colSpan={defaultColSpan}
                        customerKind={CustomerKind.Local}
                        gutter={[16, 16]}
                        hasGuarantorPreset={false}
                        hasUploadDocuments={false}
                        hasVSOUpload={false}
                        kycExtraSettings={kycExtraSettings}
                        kycPresets={kycState}
                        removeDocument={null}
                        resetFormHandler={resetFormHandler}
                        setIsCorporate={null}
                        setPrefill={null}
                        showCommentsToInsurer={false}
                        showRemarks={false}
                        showTitle={false}
                        uploadDocument={null}
                        withFinancing={false}
                        wrapperGutter={[24, 24]}
                        isEvent
                    />
                    <ConsentsAndDeclarations applicationAgreements={agreement.data} />
                </Space>
            </Container>
        </Form>
    );
};

const KYCContainer = styled.div`
    margin-bottom: 32px;
`;
type NewContactKYCFormProps = StateAndDispatch &
    Partial<UploadDocumentProp> & {
        onClose: SearchCapCustomerKYCProps['onClose'];
    };

const NewContactKYC = ({ state, dispatch, onClose }: NewContactKYCFormProps) => {
    const { t } = useTranslation('launchpadLeadList');
    const { notification } = useThemeComponents();
    const apolloClient = useApolloClient();
    const { currentLanguageId } = useLanguage();
    const navigate = useNavigate();

    const {
        kycExtraSettings,
        applicationModuleId,
        countryCode,
        dealerId,
        endpointId,
        selectedBusinessPartner,
        searchedCustomer,
    } = state;

    // Fetch KYC
    const {
        data: kycData,
        loading: kycLoading,
        error: kycError,
    } = usePrefetchKycFieldsForContactQuery({
        fetchPolicy: 'cache-and-network',
        variables: { applicationModuleId },
        skip: !applicationModuleId,
    });

    const kycState = useMemo(
        () => (kycData && !kycLoading ? getApplicantKyc(kycData.applicantKyc ?? []) : []),
        [kycData, kycLoading]
    );

    // Fetch Agreements
    const { data: agreementData, loading: agreementLoading } = usePrefetchAgreementsForContactQuery({
        fetchPolicy: 'cache-and-network',
        variables: { applicationModuleId },
        skip: !applicationModuleId,
    });

    const agreement = useMemo(() => {
        if (agreementData && !agreementLoading) {
            const activeAgreements: ApplicationAgreementDataFragment[] = getApplicantAgreements(
                agreementData.applicantAgreements
            );

            // Get remaining agreements that align with active one
            const newAgreementValues = getAgreementValues(mergeAgreementValues(activeAgreements, {}));

            return { values: newAgreementValues, data: activeAgreements };
        }

        return { values: null, data: [] };
    }, [agreementData, agreementLoading]);

    const applicantValidations = useNewContactValidator({
        applicationAgreements: agreement?.data || [],
        applicationKycPresets: kycState || [],
        extraSettings: kycExtraSettings,
        countryCode,
    });
    const validate = useValidator(applicantValidations);

    const isLoading = useMemo(() => kycLoading || agreementLoading, [agreementLoading, kycLoading]);

    const initialValues: ApplicationKYCAndAgreement = useMemo(
        () => ({ agreements: agreement.values, customer: { fields: {} }, tradeInVehicle: [], capValues: {} }),
        [agreement?.values]
    );

    const onSubmitKyc = useHandleError(
        async (values: ApplicationKYCAndAgreement) => {
            notification.loading({
                content: t('launchpadLeadList:newContactModal.messages.createNewContact'),
                duration: 0,
                key: 'primary',
            });

            const { customerAssetArray } = refineCustomerValues(values.customer.fields);
            const contactDocuments = customerAssetArray.map(asset =>
                asset.files
                    .map(file => {
                        if (file instanceof File) {
                            return { upload: file, kind: asset.type };
                        }

                        return null;
                    })
                    .filter(Boolean)
            );

            const tradeInVehicle = (values?.tradeInVehicle || []).filter(Boolean).map(vehicle => ({
                ...vehicle,
                source: vehicle.source || LocalCustomerFieldSource.UserInput,
            }));

            const isCustomerSearchPerformed = !!selectedBusinessPartner || hasSearchedCustomerValues(searchedCustomer);

            const { errors, data } = await apolloClient.mutate<
                CreateNewContactFromBpMutation,
                CreateNewContactFromBpMutationVariables
            >({
                mutation: CreateNewContactFromBpDocument,
                variables: {
                    applicationModuleId,
                    dealerId,
                    capValues: values.capValues,
                    tradeInVehicle,
                    customerAgreements: Object.entries(values.agreements)
                        .map(([consentId, agreementFields]) => {
                            if (agreementFields.isAgreed) {
                                if (agreementFields.platformsAgreed) {
                                    return { id: consentId, platformsAgreed: agreementFields.platformsAgreed };
                                }

                                return { id: consentId };
                            }

                            return null;
                        })
                        .filter(Boolean),
                    contactData: prepareKYCFieldPayload(values.customer.fields),
                    contactDocuments: flatten(contactDocuments).filter(Boolean),
                    endpointId,
                    languageId: currentLanguageId,
                    isCustomerSearchPerformed,
                },
            });

            if (!errors && data?.result) {
                notification.success({
                    content: t('launchpadLeadList:newContactModal.messages.createNewContactSuccess'),
                    key: 'primary',
                });
                onClose();
                navigate(`./leads/${data.result.versioning.suiteId}`);
            }
        },
        [
            notification,
            t,
            selectedBusinessPartner,
            searchedCustomer,
            apolloClient,
            applicationModuleId,
            dealerId,
            endpointId,
            currentLanguageId,
            onClose,
            navigate,
        ]
    );

    if (isLoading) {
        return (
            <Col span={24}>
                <center>
                    <LoadingElement />
                </center>
            </Col>
        );
    }

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmitKyc} validate={validate}>
            <KYCContainer>
                <Inner
                    agreement={agreement}
                    dispatch={dispatch}
                    kycError={!!kycError}
                    kycExtraSettings={kycExtraSettings}
                    kycState={kycState}
                    state={state}
                />
            </KYCContainer>
        </Formik>
    );
};
export default NewContactKYC;
