import { PTag } from '@porsche-design-system/components-react';
import { Divider } from 'antd';
import { useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { CompanyTheme } from '../../../../api/types';
import CheckboxField from '../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../themes/hooks';
import renderMarkdown from '../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { CNDStyledTextSpan } from '../ui';
import AgreementField, { StyledCard } from './AgreementField';
import { CheckboxContainer as BaseCheckboxContainer, hasModalLegalText } from './shared';

const ChildrenContainer = styled.div`
    display: flex;
    flex-direction: column;
`;

const CheckboxContainer = styled(BaseCheckboxContainer)`
    & .ant-form-item {
        margin-bottom: 16px;
    }
`;

// Fallback tag styling for non-PorscheV3 themes
const FallbackTag = styled.div`
    display: inline-block;
    padding: 4px 12px;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #595959;
    margin-bottom: 24px;
`;

const StyledDivider = styled(Divider)`
    border-color: #d8d8db;
`;

export type GroupAgreementContainerProps = {
    group: ApplicationAgreementDataFragment;
    childAgreements: ApplicationAgreementDataFragment[];
    prefix?: string;
    disabled?: boolean;
    editable?: boolean;
};

const GroupAgreementContainer: React.FC<GroupAgreementContainerProps> = ({
    group,
    childAgreements,
    prefix,
    disabled = false,
    editable = true,
}) => {
    const translatedString = useTranslatedString();
    const { Checkbox, ConsentModal, LegalText, theme } = useThemeComponents();
    const { setTouched, setFieldValue, setValues, values } = useFormikContext();

    // Modal state management
    const [currentModalIndex, setCurrentModalIndex] = useState<number>(-1);
    const [isModalSequenceActive, setIsModalSequenceActive] = useState(false);
    const [modalDecisions, setModalDecisions] = useState<Record<string, boolean>>({});

    // Get children that have modal legal text
    const childrenWithModals = childAgreements.filter(child => hasModalLegalText(child, translatedString));

    const parentFieldName = prefix ? `${prefix}.${group.id}` : group.id;
    const childFieldNames = childAgreements.map(child =>
        prefix ? `${prefix}.${child.id}.isAgreed` : `${child.id}.isAgreed`
    );

    const isParentChecked = get(`${parentFieldName}.isAgreed`, values) || false;
    const areAllChildrenChecked = childFieldNames.every(fieldName => get(fieldName, values) === true);

    const hasParentBeenToggled = useRef(false);

    const handleParentChange = useCallback(
        (checked: boolean) => {
            // Only batch mark all child fields as touched the first time parent is toggled
            if (!hasParentBeenToggled.current) {
                setTouched((prevTouched: any) => {
                    const updatedTouched = { ...prevTouched };
                    childAgreements.forEach(child => {
                        const fieldName = prefix ? `${prefix}.${child.id}.isAgreed` : `${child.id}.isAgreed`;
                        updatedTouched[fieldName] = true;
                    });

                    return updatedTouched;
                }, false);
                hasParentBeenToggled.current = true;
            }

            if (checked && childrenWithModals.length > 0) {
                // Start modal sequence when parent is checked and there are children with modals
                setIsModalSequenceActive(true);
                setCurrentModalIndex(0);
                setModalDecisions({}); // Reset modal decisions for new sequence
                // Don't set parent as checked yet - wait for modal sequence completion
            } else {
                // Handle unchecking or when no modals needed
                setValues((prevValues: any) => {
                    const updated = { ...prevValues };
                    updated.agreements = { ...updated.agreements };

                    updated.agreements[group.id] = {
                        ...updated.agreements[group.id],
                        isAgreed: checked,
                    };

                    childAgreements.forEach(child => {
                        updated.agreements[child.id] = {
                            ...updated.agreements[child.id],
                            isAgreed: checked,
                        };
                    });

                    return updated;
                });
            }
        },
        [childAgreements, childrenWithModals.length, setTouched, setValues, prefix, group.id]
    );

    const onModalSequenceCompleted = useCallback(
        (updatedDecisions: Record<string, boolean>) => {
            // Sequence complete - apply final state logic
            const allModalChildrenAgreed = childrenWithModals.every(child => updatedDecisions[child.id] === true);

            setValues((prevValues: any) => {
                const updated = { ...prevValues };
                updated.agreements = { ...updated.agreements };

                // Set parent
                updated.agreements[group.id] = {
                    ...updated.agreements[group.id],
                    isAgreed: allModalChildrenAgreed,
                };

                // Set children
                childAgreements.forEach(child => {
                    const fieldName = child.id;
                    const hasModal = hasModalLegalText(child, translatedString);

                    let isAgreedValue: boolean;
                    if (allModalChildrenAgreed) {
                        isAgreedValue = true;
                    } else if (hasModal) {
                        isAgreedValue = updatedDecisions[child.id] || false;
                    } else {
                        isAgreedValue = true;
                    }

                    updated.agreements[fieldName] = {
                        ...updated.agreements[fieldName],
                        isAgreed: isAgreedValue,
                    };
                });

                return updated;
            });

            // Reset modal state
            setIsModalSequenceActive(false);
            setCurrentModalIndex(-1);
            setModalDecisions({});
        },
        [childrenWithModals, setValues, group.id, childAgreements, translatedString]
    );

    // Modal action handlers
    const handleModalOk = useCallback(() => {
        if (currentModalIndex >= 0 && currentModalIndex < childrenWithModals.length) {
            const currentChild = childrenWithModals[currentModalIndex];

            // Record the decision for this child
            const updatedDecisions = {
                ...modalDecisions,
                [currentChild.id]: true,
            };
            setModalDecisions(updatedDecisions);

            // Move to next modal or complete sequence
            if (currentModalIndex < childrenWithModals.length - 1) {
                setCurrentModalIndex(currentModalIndex + 1);
            } else {
                onModalSequenceCompleted(updatedDecisions);
            }
        }
    }, [currentModalIndex, childrenWithModals, modalDecisions, onModalSequenceCompleted]);

    const handleModalDisagree = useCallback(() => {
        if (currentModalIndex >= 0 && currentModalIndex < childrenWithModals.length) {
            const currentChild = childrenWithModals[currentModalIndex];

            // Record the decision for this child
            const updatedDecisions = {
                ...modalDecisions,
                [currentChild.id]: false,
            };
            setModalDecisions(updatedDecisions);

            // Move to next modal or complete sequence
            if (currentModalIndex < childrenWithModals.length - 1) {
                setCurrentModalIndex(currentModalIndex + 1);
            } else {
                onModalSequenceCompleted(updatedDecisions);
            }
        }
    }, [currentModalIndex, childrenWithModals, modalDecisions, onModalSequenceCompleted]);

    useEffect(() => {
        if (areAllChildrenChecked && !isParentChecked && !isModalSequenceActive) {
            setFieldValue(`${parentFieldName}.isAgreed`, true);
        } else if (!areAllChildrenChecked && isParentChecked && !isModalSequenceActive) {
            setFieldValue(`${parentFieldName}.isAgreed`, false);
        }
    }, [areAllChildrenChecked, isParentChecked, parentFieldName, setFieldValue, isModalSequenceActive]);

    // Helper functions for modal content
    const getCurrentModalChild = useMemo(() => {
        if (currentModalIndex >= 0 && currentModalIndex < childrenWithModals.length) {
            return childrenWithModals[currentModalIndex];
        }

        return null;
    }, [currentModalIndex, childrenWithModals]);

    const getModalTitle = () => {
        if (!getCurrentModalChild) {
            return '';
        }

        return translatedString(getCurrentModalChild.title) || '';
    };

    const createCustomHeader = () => {
        if (childrenWithModals.length <= 1) {
            return null;
        }

        const consentText = `Consent ${currentModalIndex + 1} of ${childrenWithModals.length}`;

        if (theme === CompanyTheme.PorscheV3) {
            return <PTag color="background-surface">{consentText}</PTag>;
        }

        return <FallbackTag>{consentText}</FallbackTag>;
    };

    const renderLegalText = (child: ApplicationAgreementDataFragment) => {
        if (child.__typename !== 'CheckboxApplicationAgreement' || !child.legalMarkup) {
            return null;
        }

        return (
            <LegalText $disabled={disabled} noPadding>
                {renderMarkdown(translatedString(child.legalMarkup))}
            </LegalText>
        );
    };

    const currentModalChild = getCurrentModalChild;

    return (
        <>
            {/* Sequential Modal for Legal Text */}
            {isModalSequenceActive && currentModalChild && (
                <ConsentModal
                    key={`${currentModalChild.id}-${currentModalIndex}`}
                    customHeader={createCustomHeader()}
                    data-cy="group-consent-modal"
                    footer={!editable ? null : undefined}
                    onCancel={handleModalDisagree}
                    onOk={handleModalOk}
                    title={getModalTitle()}
                    centered
                    closable
                    open
                >
                    {renderLegalText(currentModalChild)}
                </ConsentModal>
            )}

            <StyledCard bordered>
                <CheckboxContainer>
                    <CheckboxField
                        checked={isParentChecked}
                        customComponent={Checkbox}
                        disabled={disabled}
                        name={`${parentFieldName}.isAgreed`}
                        onChange={e => handleParentChange(e.target.checked)}
                    >
                        <CNDStyledTextSpan $disabled={disabled}>
                            {renderMarkdown(translatedString(group.description))}
                        </CNDStyledTextSpan>
                    </CheckboxField>
                </CheckboxContainer>

                <StyledDivider />

                <ChildrenContainer>
                    {childAgreements.map((child, index) => (
                        <>
                            <AgreementField
                                key={child.id}
                                agreement={child}
                                bordered={false}
                                disabled={disabled}
                                editable={editable}
                                prefix={prefix}
                            />
                            {/* Only show divider if not the last item */}
                            {index !== childAgreements.length - 1 && <StyledDivider />}
                        </>
                    ))}
                </ChildrenContainer>
            </StyledCard>
        </>
    );
};

export default GroupAgreementContainer;
