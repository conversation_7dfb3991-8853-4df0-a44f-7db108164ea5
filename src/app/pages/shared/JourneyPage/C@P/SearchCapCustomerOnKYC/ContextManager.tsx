import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { LeadDataFragment } from '../../../../../api/fragments/LeadData';
import {
    ButtonClickedAction,
    type ButtonClickedData,
    type SelectedCapValues,
    ViewState,
} from '../../../../../components/cap/searchCustomersAndLeads/types';
import useReducer, {
    type SearchedCustomer,
    SearchCapCustomerType,
} from '../../../../../components/cap/searchCustomersAndLeads/useReducer';
import { hasSearchedCustomerValues } from '../../../../../components/cap/searchCustomersAndLeads/utils';
import { SearchCapCustomerContext } from './shared';
import SearchCapCustomerKYC from '.';

type SearchCapCustomerContextManagerProps = {
    lead?: LeadDataFragment;
    kycHasCompleted: boolean;
    applicationRequireLogin: boolean;
    applicationModuleId: string;
    eventId?: string;
    capModuleId: string;
    countryCode: string;
    dealerId: string;
    children: React.ReactNode;
    onCapValuesChanged?: (capValues: SelectedCapValues) => void;
    onCapModalErrorConfirmed?: () => void;
    onCustomerSearchPerformed?: (isPerformed: boolean) => void;
    onSearchedCustomerDataAvailable?: (searchedCustomer: SearchedCustomer) => void;
};

// eslint-disable-next-line import/prefer-default-export
export const SearchCapCustomerContextManager: React.FC<SearchCapCustomerContextManagerProps> = ({
    lead,
    kycHasCompleted,
    applicationRequireLogin,
    applicationModuleId,
    capModuleId,
    eventId,
    dealerId,
    countryCode,
    children,
    onCapValuesChanged,
    onCapModalErrorConfirmed,
    onCustomerSearchPerformed,
    onSearchedCustomerDataAvailable,
}: SearchCapCustomerContextManagerProps) => {
    const [state, dispatch] = useReducer();
    const { visible, selectedLead, selectedBusinessPartner, userChooseCreateNew, viewState, searchedCustomer } = state;
    const [componentClosable, setComponentCloseable] = useState<boolean>(true);

    const showSearchComponent = useCallback(
        (closable?: boolean) => {
            const hasSearchedCustomerValuesPresent = hasSearchedCustomerValues(searchedCustomer);

            dispatch({
                type: 'setStateValues',
                values: {
                    visible: true,
                    lead,
                    applicationModuleId,
                    eventId,
                    capModuleId,
                    dealerId,
                    countryCode,
                    viewState:
                        !selectedBusinessPartner && !hasSearchedCustomerValuesPresent
                            ? ViewState.SearchForm
                            : viewState,
                },
            });
            setComponentCloseable(closable);
        },
        [
            searchedCustomer,
            lead,
            applicationModuleId,
            eventId,
            capModuleId,
            dealerId,
            countryCode,
            selectedBusinessPartner,
            viewState,
        ]
    );

    const hideSearchComponent = useCallback(() => {
        dispatch({
            type: 'setStateValues',
            values: {
                visible: false,
                searchCapCustomerType: SearchCapCustomerType.Email,
                searchedCustomer: {
                    email: null,
                    phone: null,
                    firstName: null,
                    lastName: null,
                    vin: null,
                    firstNameJapan: null,
                    lastNameJapan: null,
                },
            },
        });
    }, [dispatch]);

    const context = useMemo(
        () => ({
            componentState: {
                open: visible,
                closeable: componentClosable,
            },
            showSearchComponent,
            hideSearchComponent,
            selectedLead,
            selectedBusinessPartner,
            userChooseCreateNew,
            searchedCustomer,
        }),
        [
            visible,
            componentClosable,
            showSearchComponent,
            hideSearchComponent,
            selectedLead,
            selectedBusinessPartner,
            userChooseCreateNew,
            searchedCustomer,
        ]
    );

    useEffect(() => {
        if (
            onSearchedCustomerDataAvailable &&
            !selectedBusinessPartner &&
            hasSearchedCustomerValues(searchedCustomer)
        ) {
            onSearchedCustomerDataAvailable(searchedCustomer);
        }
    }, [onSearchedCustomerDataAvailable, searchedCustomer, selectedBusinessPartner]);

    const onButtonClicked = useCallback(
        (data: ButtonClickedData) => {
            const { action } = data;
            switch (action) {
                case ButtonClickedAction.SelectBP: {
                    return onCapValuesChanged({
                        selectedBusinessPartner: data.selectedBusinessPartner,
                        selectedValue: ViewState.BusinessPartner,
                    });
                }
                case ButtonClickedAction.SelectLead: {
                    return onCapValuesChanged({
                        selectedBusinessPartner: data.selectedBusinessPartner,
                        selectedLead: data.selectedLead,
                        selectedValue: ViewState.Lead,
                    });
                }
                case ButtonClickedAction.ErrorSearchConfirmed: {
                    return onCapModalErrorConfirmed();
                }
                default: {
                    return null;
                }
            }
        },
        [onCapModalErrorConfirmed, onCapValuesChanged]
    );

    return (
        <SearchCapCustomerContext.Provider value={context}>
            {children}
            {!kycHasCompleted && capModuleId && applicationRequireLogin && dealerId && (
                <SearchCapCustomerKYC
                    closable={componentClosable}
                    dispatch={dispatch}
                    onButtonClicked={onButtonClicked}
                    onClose={hideSearchComponent}
                    onCustomerSearchPerformed={onCustomerSearchPerformed}
                    state={state}
                />
            )}
        </SearchCapCustomerContext.Provider>
    );
};
