import { useMemo } from 'react';
import * as permissionKind from '../../shared/permissions';
import { ModuleType, PorscheRetainModuleSpecsFragment } from '../api';
import { useAccount } from '../components/contexts/AccountContextManager';
import { useCompanyContext } from '../components/contexts/CompanyContextManager';
import hasPermissions from '../utilities/hasPermissions';

const useConsolePermissions = () => {
    const { permissions: accountPermissions } = useAccount();

    const { companies } = useCompanyContext();

    return useMemo(() => {
        const hasCompanies = companies.length > 0;

        const hasModuleManagement = hasCompanies
            ? companies.some(
                  company =>
                      hasPermissions(company.permissions, [permissionKind.createModule]) ||
                      hasPermissions(company.permissions, [permissionKind.updateModule])
              )
            : hasPermissions(accountPermissions, [permissionKind.createModule]);

        const hasRouterManagement = hasCompanies
            ? companies.some(company => hasPermissions(company.permissions, [permissionKind.viewRouters]))
            : hasPermissions(accountPermissions, [permissionKind.viewRouters]);

        const hasUserManagement = hasCompanies
            ? companies.some(company => hasPermissions(company.permissions, [permissionKind.viewUsers]))
            : hasPermissions(accountPermissions, [permissionKind.viewUsers]);

        const hasRolesManagement = hasCompanies
            ? companies.some(company => hasPermissions(company.permissions, [permissionKind.viewRoles]))
            : hasPermissions(accountPermissions, [permissionKind.viewRoles]);

        const hasUserGroupManagement = hasCompanies
            ? companies.some(company => hasPermissions(company.permissions, [permissionKind.viewUserGroups]))
            : hasPermissions(accountPermissions, [permissionKind.viewUserGroups]);

        const viewSalesControlBoard = (() => {
            if (!hasCompanies) {
                return false;
            }

            if (companies.length > 1) {
                return (
                    companies.some(company => company.availableModules.hasSalesControlBoardModule) &&
                    (hasPermissions(accountPermissions, [permissionKind.viewSalesControlBoardManager]) ||
                        hasPermissions(accountPermissions, [permissionKind.viewSalesControlBoardSalesConsultant]))
                );
            }

            return (
                companies[0].modules.find(module => module.__typename === ModuleType.SalesControlBoardModule) &&
                (hasPermissions(accountPermissions, [permissionKind.viewSalesControlBoardManager]) ||
                    hasPermissions(accountPermissions, [permissionKind.viewSalesControlBoardSalesConsultant]))
            );
        })();

        const hasDealerManagement = companies.some(company =>
            hasPermissions(company.permissions, [permissionKind.viewDealers])
        );

        const hasVehicleManagement = companies.some(
            company =>
                company.availableModules.hasVehicleManagementModules &&
                hasPermissions(company.permissions, [permissionKind.viewVehicles])
        );

        const hasFinderVehicleManagement = companies.some(
            company =>
                company.availableModules.hasFinderVehicleManagementModules &&
                hasPermissions(company.permissions, [permissionKind.viewFinderVehicles])
        );

        const hasFinancing = companies.some(
            company =>
                company.availableModules.hasApplicationModules &&
                company.availableModules.hasBanks &&
                hasPermissions(company.permissions, [permissionKind.viewFinanceApplications])
        );

        const hasReservation = companies.some(
            company =>
                company.availableModules.hasApplicationModules &&
                company.availableModules.hasPaymentModules &&
                hasPermissions(company.permissions, [permissionKind.viewReservations])
        );

        const canViewLeads = accountPermissions.includes(permissionKind.viewLeads);
        const canViewContacts = accountPermissions.includes(permissionKind.viewContact);

        const hasLeadsAndContacts =
            canViewLeads &&
            canViewContacts &&
            companies.some(
                company =>
                    company.availableModules.hasApplicationModules &&
                    hasPermissions(company.permissions, [permissionKind.viewLeads, permissionKind.viewContact])
            );

        const hasLeads =
            !hasLeadsAndContacts &&
            canViewLeads &&
            companies.some(
                company =>
                    company.availableModules.hasApplicationModules &&
                    hasPermissions(company.permissions, [permissionKind.viewLeads])
            );

        const hasContacts =
            !hasLeadsAndContacts &&
            canViewContacts &&
            companies.some(
                company =>
                    company.availableModules.hasApplicationModules &&
                    hasPermissions(company.permissions, [permissionKind.viewContact])
            );

        const hasInsurance = companies.some(
            company =>
                company.availableModules.hasApplicationModules &&
                company.availableModules.hasInsuranceModules &&
                hasPermissions(company.permissions, [permissionKind.viewInsuranceApplications])
        );

        const hasAppointments = companies.some(
            company =>
                company.availableModules.hasAppointmentModules &&
                hasPermissions(company.permissions, [permissionKind.viewAppointments])
        );

        const hasVisitAppointments = companies.some(
            company =>
                company.availableModules.hasVisitAppointmentModules &&
                hasPermissions(company.permissions, [permissionKind.viewVisitAppointments])
        );

        // This one is only for mobility booking submission
        const hasBookings = companies.some(
            company =>
                company.availableModules.hasMobilityModules &&
                hasPermissions(company.permissions, [permissionKind.viewBookingApplications])
        );

        const accessConfigurators = companies.some(
            company =>
                company.availableModules.hasConfiguratorModules &&
                hasPermissions(company.permissions, [permissionKind.viewConfigurators])
        );

        const accessEvents = companies.some(
            company =>
                company.availableModules.hasEventModules &&
                hasPermissions(company.permissions, [permissionKind.viewEvents])
        );

        const accessPromotions = companies.some(
            company =>
                company.availableModules.hasPromotionCodeModules &&
                hasPermissions(company.permissions, [permissionKind.viewPromoCodes])
        );

        const accessInventories = companies.some(
            company =>
                company.availableModules.hasInventories &&
                hasPermissions(company.permissions, [permissionKind.viewInventories])
        );

        const accessMaintenances = companies.some(
            company =>
                company.activeMaintenanceModule &&
                hasPermissions(company.permissions, [permissionKind.manageMaintenance])
        );

        const accessInsurers = companies.some(
            company =>
                company.availableModules.hasInsuranceModules &&
                hasPermissions(company.permissions, [permissionKind.manageInsurers])
        );

        const accessGiftVoucher = companies.some(
            company =>
                company.availableModules.hasGiftVoucherModules &&
                hasPermissions(company.permissions, [permissionKind.viewGiftVouchers])
        );

        const hasTradeIn = companies.some(
            company =>
                company.availableModules.hasTradeInModules &&
                hasPermissions(company.permissions, [permissionKind.viewTradeIns])
        );

        const hasApplicationTradeIn = companies.some(
            company =>
                company.modules.some(
                    module => module.__typename === ModuleType.LaunchPadModule && module.hasTradeInRequest
                ) && hasPermissions(company.permissions, [permissionKind.viewApplicationTradeIns])
        );

        const accessAgreements = companies.some(
            company =>
                company.availableModules.hasAgreementModules &&
                hasPermissions(company.permissions, [permissionKind.viewAgreements])
        );

        const accessBanks = companies.some(
            company =>
                company.availableModules.hasSystemBanks &&
                hasPermissions(company.permissions, [permissionKind.manageBanks])
        );

        const accessFinanceProducts = companies.some(
            company =>
                company.availableModules.hasFinanceProducts &&
                hasPermissions(company.permissions, [permissionKind.viewFinanceProducts])
        );

        const accessInsuranceProduct = companies.some(
            company =>
                company.availableModules.hasInsuranceModules &&
                hasPermissions(company.permissions, [permissionKind.viewInsuranceProducts])
        );

        const accessLabels = companies.some(
            company =>
                company.availableModules.hasLabelsModules &&
                hasPermissions(company.permissions, [permissionKind.viewLabels])
        );

        const accessMobility = companies.some(
            company =>
                company.availableModules.hasMobilityModules &&
                hasPermissions(company.permissions, [permissionKind.viewMobilities])
        );

        const accessWebpages = companies.some(
            company =>
                company.availableModules.hasWebsiteModules &&
                hasPermissions(company.permissions, [permissionKind.viewWebpages])
        );

        const viewDashboard = companies.some(company =>
            hasPermissions(company.permissions, [permissionKind.viewDashboard])
        );

        const viewMarketingDashboard = companies.some(
            company =>
                company.availableModules.hasMarketingModules &&
                hasPermissions(company.permissions, [permissionKind.viewMarketingDashboard])
        );

        // temporary remove and disable VF-1211
        // const hasCustomerModules = companies.some(
        //     company =>
        //         company.availableModules.hasCustomerModules &&
        //         hasPermissions(company.permissions, [permissionKind.viewCustomers])
        // );

        const hasCustomerModules = false;

        const accessCts = companies.some(
            company =>
                company.availableModules.hasCtsModules &&
                hasPermissions(company.permissions, [permissionKind.manageCts])
        );

        const [accessPorscheRetain, porscheRetainLink] = (() => {
            const accessPR = companies.some(
                company =>
                    company.availableModules.hasPorscheRetainModules &&
                    hasPermissions(company.permissions, [permissionKind.viewPorscheRetain])
            );

            if (!accessPR) {
                return [false, ''];
            }

            if (companies.length === 1) {
                const { modules } = companies[0];

                const find = modules.find(i => i.__typename === ModuleType.PorscheRetainModule);

                if (!find) {
                    return [false, ''];
                }

                return [true, (find as PorscheRetainModuleSpecsFragment).link];
            }

            return [true, ''];
        })();

        const viewCampaigns = companies.some(
            company =>
                hasPermissions(company.permissions, [permissionKind.manageCampaigns]) &&
                company.availableModules.hasCapModules
        );

        return {
            hasModuleManagement,
            hasRouterManagement,
            hasUserManagement,
            hasRolesManagement,
            hasUserGroupManagement,
            hasDealerManagement,
            hasVehicleManagement,
            hasFinderVehicleManagement,
            hasFinancing,
            hasReservation,
            hasLeadsAndContacts,
            hasLeads,
            hasContacts,
            hasInsurance,
            hasAppointments,
            hasVisitAppointments,
            hasBookings,
            accessConfigurators,
            accessEvents,
            accessPromotions,
            accessInventories,
            accessMaintenances,
            accessInsurers,
            accessGiftVoucher,
            hasTradeIn,
            hasApplicationTradeIn,
            accessAgreements,
            accessBanks,
            accessFinanceProducts,
            accessInsuranceProduct,
            accessLabels,
            accessMobility,
            accessWebpages,
            viewDashboard,
            viewMarketingDashboard,
            viewSalesControlBoard,
            hasCustomerModules,
            accessCts,
            accessPorscheRetain,
            porscheRetainLink,
            viewCampaigns,
        };
    }, [companies, accountPermissions]);
};
export default useConsolePermissions;
