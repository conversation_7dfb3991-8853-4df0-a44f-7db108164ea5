import type { TFunction } from 'i18next';
import { ObjectId } from 'mongodb';
import type { Campaign } from '../../../../database/documents/Campaign';
import { ModuleType } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const validateCampaignUsage = async (campaign: Campaign, t: TFunction) => {
    const { collections } = await getDatabaseContext();

    const moduleUsage = await collections.modules.findOne({
        _type: {
            $in: [
                ModuleType.StandardApplicationModule,
                ModuleType.ConfiguratorModule,
                ModuleType.FinderApplicationPrivateModule,
                ModuleType.FinderApplicationPublicModule,
            ],
        },
        leadCampaignId: campaign._id,
    });

    if (moduleUsage) {
        throw new Error(
            t('errors:campaign.campaignInUsedByModule', {
                campaignId: campaign.campaignId,
                moduleName: moduleUsage.displayName,
            })
        );
    }

    const eventUsage = await collections.events.findOne({
        $or: [
            {
                'utmParametersSettings.defaultValue.capCampaignId': campaign._id,
            },
            {
                'utmParametersSettings.overrides.capCampaignId': campaign._id,
            },
        ],
    });

    if (eventUsage) {
        throw new Error(
            t('errors:campaign.campaignInUsedByEvent', {
                campaignId: campaign.campaignId,
                eventName: eventUsage.displayName,
            })
        );
    }
};

const mutation: GraphQLMutationResolvers['deleteCampaign'] = async (
    root,
    { id },
    { getPermissionController, getUser, getTranslations }
) => {
    const { t } = await getTranslations(['errors']);
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser();

    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    const campaign = await collections.campaigns.findOne({
        _id: id,
        isDeleted: false,
        ...permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.Delete),
    });

    if (!campaign) {
        throw new Error(t('errors:campaign.notFound'));
    }

    await validateCampaignUsage(campaign, t);

    const deletedCampaign = await collections.campaigns.findOneAndUpdate(
        {
            $and: [
                { _id: id, isDeleted: false },
                permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.Delete),
            ],
        },
        {
            $set: {
                isDeleted: true,
                ...versioning,
            },
        },
        { returnDocument: 'after' }
    );

    return !!deletedCampaign;
};

export default requiresLoggedUser(mutation);
